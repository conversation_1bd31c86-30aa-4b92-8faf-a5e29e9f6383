# YouTube Shorts Downloader Bot

Automatically downloads YouTube shorts from channels listed in `list.txt` with complete metadata extraction for AI-powered content creation.

## Features

- ✅ Downloads last 100 videos from each channel (shorts only, <60 seconds)
- ✅ Best available resolution (up to 1080p)
- ✅ Extracts comprehensive metadata: title, description, tags, view count, thumbnail URL
- ✅ Organizes videos by channel name in separate folders
- ✅ Creates consolidated metadata.json for each channel
- ✅ Rate limiting to avoid getting blocked
- ✅ Error handling and progress tracking
- ✅ Cross-platform filename sanitization

## Installation

### Option 1: Quick Install (Windows)
1. Run `install_dependencies.bat`

### Option 2: Manual Install
1. Install Python packages:
   ```bash
   pip install -r requirements.txt
   ```

2. Install FFmpeg (required for video processing):
   - Download from: https://ffmpeg.org/download.html
   - Or use package manager:
     ```bash
     # Windows (chocolatey)
     choco install ffmpeg
     
     # Windows (winget)
     winget install ffmpeg
     ```

## Usage

1. **Prepare channel list**: Edit `list.txt` with YouTube channel URLs (one per line)
   ```
   https://www.youtube.com/@CuteBabyCats267/shorts
   https://www.youtube.com/@Sonyakisa8/shorts
   ```

2. **Run the downloader**:
   ```bash
   python youtube_shorts_downloader.py
   ```

3. **Check results**: Videos will be downloaded to `downloads/` folder organized by channel name

## Output Structure

```
downloads/
├── CuteBabyCats267/
│   ├── Cute Kitten Playing.mp4
│   ├── Funny Cat Moments.mp4
│   ├── metadata.json
│   └── thumbnails/
└── Sonyakisa8/
    ├── Amazing Cat Tricks.mp4
    ├── metadata.json
    └── thumbnails/
```

## Metadata Format

Each channel gets a `metadata.json` file containing:

```json
{
  "channel_name": "CuteBabyCats267",
  "download_date": "2024-01-15T10:30:00",
  "total_videos": 45,
  "videos": [
    {
      "title": "Cute Kitten Playing",
      "description": "Watch this adorable kitten...",
      "tags": ["cats", "kittens", "cute", "pets"],
      "view_count": 125000,
      "duration": 45,
      "upload_date": "20240110",
      "uploader": "CuteBabyCats267",
      "video_id": "dQw4w9WgXcQ",
      "thumbnail_url": "https://i.ytimg.com/vi/...",
      "webpage_url": "https://www.youtube.com/watch?v=...",
      "filename": "Cute Kitten Playing"
    }
  ]
}
```

## Configuration

Edit the script to customize:
- `rate_limit_delay`: Seconds between downloads (default: 2)
- `base_dir`: Download directory (default: "downloads")
- Video quality and filters

## For AI Content Creation

The extracted metadata is perfect for:
- **Title Generation**: Use existing titles as inspiration
- **SEO Optimization**: Analyze successful tags and descriptions
- **Content Planning**: Identify popular video types by view count
- **Thumbnail Creation**: Download thumbnails for AI analysis
- **Trend Analysis**: Track upload dates and performance

## Troubleshooting

- **"yt-dlp not found"**: Install with `pip install yt-dlp`
- **FFmpeg errors**: Make sure FFmpeg is installed and in PATH
- **Rate limiting**: Increase `rate_limit_delay` if getting blocked
- **No videos downloaded**: Check if channels actually have shorts (<60s videos)

## Legal Notice

This tool is for educational and research purposes. Ensure you comply with:
- YouTube's Terms of Service
- Copyright laws in your jurisdiction
- Fair use guidelines when creating derivative content
- Proper attribution when required
