#!/usr/bin/env python3
"""
YouTube Shorts Downloader Bo<PERSON>
Downloads recent 100 shorts from channels listed in list.txt
Organizes videos by channel with metadata extraction
"""

import json
import time
import subprocess
import sys
from pathlib import Path
from datetime import datetime

class YouTubeShortsDownloader:
    def __init__(self, list_file="list.txt", base_dir="downloads"):
        self.list_file = list_file
        self.base_dir = Path(base_dir)
        self.base_dir.mkdir(exist_ok=True)
        self.rate_limit_delay = 2  # seconds between downloads
        self.progress_file = "download_progress.json"

    def load_progress(self):
        """Load download progress from file"""
        try:
            if Path(self.progress_file).exists():
                with open(self.progress_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            print(f"⚠️ Error loading progress: {e}")
        return {"completed_channels": []}

    def save_progress(self, completed_channels):
        """Save download progress to file"""
        try:
            progress = {"completed_channels": completed_channels}
            with open(self.progress_file, 'w', encoding='utf-8') as f:
                json.dump(progress, f, indent=2)
        except Exception as e:
            print(f"⚠️ Error saving progress: {e}")

    def is_channel_completed(self, channel_url):
        """Check if channel has already been downloaded"""
        channel_name = self.extract_channel_name(channel_url)
        channel_dir = self.base_dir / channel_name
        metadata_file = channel_dir / 'metadata.json'
        return metadata_file.exists()

    def read_channel_list(self):
        """Read channel URLs from list.txt"""
        try:
            with open(self.list_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            channels = []
            for line in lines:
                line = line.strip()
                if line and not line.startswith('#') and 'youtube.com' in line:
                    # Clean up the URL - remove comments after -
                    url = line.split(' -')[0].strip()
                    channels.append(url)
            
            return channels
        except FileNotFoundError:
            print(f"Error: {self.list_file} not found!")
            return []
    
    def sanitize_filename(self, filename):
        """Sanitize filename for Windows/cross-platform compatibility"""
        # Remove or replace invalid characters
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')
        
        # Limit length and remove trailing dots/spaces
        filename = filename[:200].strip('. ')
        return filename
    
    def extract_channel_name(self, url):
        """Extract channel name from URL"""
        try:
            # Try to get channel name from URL
            if '@' in url:
                channel_name = url.split('@')[1].split('/')[0]
            else:
                # Fallback to using yt-dlp to get channel name
                cmd = [
                    'yt-dlp',
                    '--print', 'channel',
                    '--playlist-end', '1',
                    url
                ]
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
                if result.returncode == 0:
                    channel_name = result.stdout.strip()
                else:
                    # Last resort - use URL part
                    channel_name = url.split('/')[-2] if url.endswith('/') else url.split('/')[-1]
            
            return self.sanitize_filename(channel_name)
        except Exception as e:
            print(f"Error extracting channel name from {url}: {e}")
            return "unknown_channel"
    
    def download_channel_shorts(self, channel_url):
        """Download shorts from a single channel"""
        print(f"\n🎬 Processing channel: {channel_url}")
        
        # Extract channel name and create directory
        channel_name = self.extract_channel_name(channel_url)
        channel_dir = self.base_dir / channel_name
        channel_dir.mkdir(exist_ok=True)
        
        print(f"📁 Channel directory: {channel_dir}")
        
        # Prepare yt-dlp command for shorts only
        output_template = str(channel_dir / "%(title)s.%(ext)s")
        
        cmd = [
            'yt-dlp',
            '--format', 'best[height<=1080]',  # Best quality up to 1080p
            '--output', output_template,
            '--write-info-json',  # Save metadata as JSON
            '--write-thumbnail',  # Download thumbnails
            '--playlist-end', '100',  # Limit to 100 videos
            '--match-filter', 'duration < 60',  # Only shorts (under 60 seconds)
            '--ignore-errors',  # Continue on errors
            '--no-overwrites',  # Don't re-download existing files
            channel_url
        ]
        
        try:
            print(f"🚀 Starting download for {channel_name}...")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=1800)  # 30 min timeout
            
            if result.returncode == 0:
                print(f"✅ Successfully downloaded shorts from {channel_name}")
            else:
                print(f"⚠️ Some issues occurred downloading from {channel_name}")
                print(f"Error output: {result.stderr}")
            
            # Process downloaded files and create consolidated metadata
            self.process_channel_metadata(channel_dir, channel_name)
            
        except subprocess.TimeoutExpired:
            print(f"⏰ Timeout downloading from {channel_name}")
        except Exception as e:
            print(f"❌ Error downloading from {channel_name}: {e}")
    
    def process_channel_metadata(self, channel_dir, channel_name):
        """Process individual video JSON files and create consolidated metadata"""
        print(f"📊 Processing metadata for {channel_name}...")

        videos_metadata = []
        json_files = list(channel_dir.glob("*.info.json"))

        for json_file in json_files:
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    video_info = json.load(f)

                # Extract ONLY the metadata you requested
                metadata = {
                    'title': video_info.get('title', 'Unknown Title'),
                    'description': video_info.get('description', ''),
                    'tags': video_info.get('tags', []),
                    'view_count': video_info.get('view_count', 0),
                    'thumbnail_url': video_info.get('thumbnail', ''),
                    'filename': json_file.stem  # Remove .info.json extension
                }

                videos_metadata.append(metadata)

            except Exception as e:
                print(f"⚠️ Error processing {json_file}: {e}")

        # Create consolidated metadata file
        consolidated_metadata = {
            'channel_name': channel_name,
            'download_date': datetime.now().isoformat(),
            'total_videos': len(videos_metadata),
            'videos': videos_metadata
        }

        metadata_file = channel_dir / 'metadata.json'
        with open(metadata_file, 'w', encoding='utf-8') as f:
            json.dump(consolidated_metadata, f, indent=2, ensure_ascii=False)

        print(f"✅ Created metadata.json with {len(videos_metadata)} videos")

        # Clean up individual JSON files to keep it clean
        for json_file in json_files:
            json_file.unlink()
    
    def run(self):
        """Main execution function"""
        print("🤖 YouTube Shorts Downloader Bot Starting...")
        print("=" * 50)

        # Read channel list
        channels = self.read_channel_list()
        if not channels:
            print("❌ No valid channels found in list.txt")
            return

        # Load progress and filter out completed channels
        completed_channels = []
        remaining_channels = []

        for channel_url in channels:
            if self.is_channel_completed(channel_url):
                completed_channels.append(channel_url)
                channel_name = self.extract_channel_name(channel_url)
                print(f"✅ Skipping {channel_name} (already completed)")
            else:
                remaining_channels.append(channel_url)

        if completed_channels:
            print(f"📋 Found {len(completed_channels)} already completed channels")

        if not remaining_channels:
            print("🎉 All channels already downloaded!")
            return

        print(f"📋 {len(remaining_channels)} channels remaining to process")

        # Process remaining channels
        for i, channel_url in enumerate(remaining_channels, 1):
            channel_name = self.extract_channel_name(channel_url)
            print(f"\n[{i}/{len(remaining_channels)}] Processing: {channel_name}")
            print(f"🔗 URL: {channel_url}")

            try:
                self.download_channel_shorts(channel_url)
                completed_channels.append(channel_url)
                self.save_progress(completed_channels)

                # Rate limiting
                if i < len(remaining_channels):  # Don't delay after last channel
                    print(f"⏳ Waiting {self.rate_limit_delay} seconds before next channel...")
                    time.sleep(self.rate_limit_delay)

            except KeyboardInterrupt:
                print("\n🛑 Download interrupted by user")
                print(f"💾 Progress saved. Resume by running the script again.")
                break
            except Exception as e:
                print(f"❌ Unexpected error processing {channel_url}: {e}")
                continue

        print("\n🎉 Download process completed!")
        print(f"📁 All downloads saved to: {self.base_dir.absolute()}")

        # Clean up progress file when everything is done
        if len(completed_channels) == len(channels):
            try:
                Path(self.progress_file).unlink()
                print("🧹 Cleaned up progress file")
            except:
                pass

def main():
    """Main entry point"""
    # Check if yt-dlp is installed
    try:
        subprocess.run(['yt-dlp', '--version'], capture_output=True, check=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ yt-dlp not found! Please install it with: pip install yt-dlp")
        sys.exit(1)
    
    # Create and run downloader
    downloader = YouTubeShortsDownloader()
    downloader.run()

if __name__ == "__main__":
    main()
