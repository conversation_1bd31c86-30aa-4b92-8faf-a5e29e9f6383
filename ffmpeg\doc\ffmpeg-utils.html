<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Created by GNU Texinfo 7.2, https://www.gnu.org/software/texinfo/ -->
  <head>
    <meta charset="utf-8">
    <title>
      FFmpeg Utilities Documentation
    </title>
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <link rel="stylesheet" type="text/css" href="bootstrap.min.css">
    <link rel="stylesheet" type="text/css" href="style.min.css">
  </head>
  <body>
    <div class="container">
      <h1>
      FFmpeg Utilities Documentation
      </h1>


<a name="SEC_Top"></a>

<div class="region-contents" id="SEC_Contents">
<h2 class="contents-heading">Table of Contents</h2>

<div class="contents">

<ul class="toc-numbered-mark">
  <li><a id="toc-Description" href="#Description">1 Description</a></li>
  <li><a id="toc-Syntax" href="#Syntax">2 Syntax</a>
  <ul class="toc-numbered-mark">
    <li><a id="toc-Quoting-and-escaping" href="#Quoting-and-escaping">2.1 Quoting and escaping</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Examples" href="#Examples">2.1.1 Examples</a></li>
    </ul></li>
    <li><a id="toc-Date" href="#Date">2.2 Date</a></li>
    <li><a id="toc-Time-duration" href="#Time-duration">2.3 Time duration</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Examples-1" href="#Examples-1">2.3.1 Examples</a></li>
    </ul></li>
    <li><a id="toc-Video-size" href="#Video-size">2.4 Video size</a></li>
    <li><a id="toc-Video-rate" href="#Video-rate">2.5 Video rate</a></li>
    <li><a id="toc-Ratio" href="#Ratio">2.6 Ratio</a></li>
    <li><a id="toc-Color" href="#Color">2.7 Color</a></li>
    <li><a id="toc-Channel-Layout" href="#Channel-Layout">2.8 Channel Layout</a></li>
  </ul></li>
  <li><a id="toc-Expression-Evaluation" href="#Expression-Evaluation">3 Expression Evaluation</a></li>
  <li><a id="toc-See-Also" href="#See-Also">4 See Also</a></li>
  <li><a id="toc-Authors" href="#Authors">5 Authors</a></li>
</ul>
</div>
</div>

<a name="Description"></a>
<h2 class="chapter">1 Description<span class="pull-right"><a class="anchor hidden-xs" href="#Description" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Description" aria-hidden="true">TOC</a></span></h2>

<p>This document describes some generic features and utilities provided
by the libavutil library.
</p>

<a name="Syntax"></a>
<h2 class="chapter">2 Syntax<span class="pull-right"><a class="anchor hidden-xs" href="#Syntax" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Syntax" aria-hidden="true">TOC</a></span></h2>

<p>This section documents the syntax and formats employed by the FFmpeg
libraries and tools.
</p>
<a class="anchor" id="quoting_005fand_005fescaping"></a><a name="Quoting-and-escaping"></a>
<h3 class="section">2.1 Quoting and escaping<span class="pull-right"><a class="anchor hidden-xs" href="#Quoting-and-escaping" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Quoting-and-escaping" aria-hidden="true">TOC</a></span></h3>

<p>FFmpeg adopts the following quoting and escaping mechanism, unless
explicitly specified. The following rules are applied:
</p>
<ul class="itemize mark-bullet">
<li>&lsquo;<samp class="samp">'</samp>&rsquo; and &lsquo;<samp class="samp">\</samp>&rsquo; are special characters (respectively used for
quoting and escaping). In addition to them, there might be other
special characters depending on the specific syntax where the escaping
and quoting are employed.

</li><li>A special character is escaped by prefixing it with a &lsquo;<samp class="samp">\</samp>&rsquo;.

</li><li>All characters enclosed between &lsquo;<samp class="samp">''</samp>&rsquo; are included literally in the
parsed string. The quote character &lsquo;<samp class="samp">'</samp>&rsquo; itself cannot be quoted,
so you may need to close the quote and escape it.

</li><li>Leading and trailing whitespaces, unless escaped or quoted, are
removed from the parsed string.
</li></ul>

<p>Note that you may need to add a second level of escaping when using
the command line or a script, which depends on the syntax of the
adopted shell language.
</p>
<p>The function <code class="code">av_get_token</code> defined in
<samp class="file">libavutil/avstring.h</samp> can be used to parse a token quoted or
escaped according to the rules defined above.
</p>
<p>The tool <samp class="file">tools/ffescape</samp> in the FFmpeg source tree can be used
to automatically quote or escape a string in a script.
</p>
<a name="Examples"></a>
<h4 class="subsection">2.1.1 Examples<span class="pull-right"><a class="anchor hidden-xs" href="#Examples" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Examples" aria-hidden="true">TOC</a></span></h4>

<ul class="itemize mark-bullet">
<li>Escape the string <code class="code">Crime d'Amour</code> containing the <code class="code">'</code> special
character:
<div class="example">
<pre class="example-preformatted">Crime d\'Amour
</pre></div>

</li><li>The string above contains a quote, so the <code class="code">'</code> needs to be escaped
when quoting it:
<div class="example">
<pre class="example-preformatted">'Crime d'\''Amour'
</pre></div>

</li><li>Include leading or trailing whitespaces using quoting:
<div class="example">
<pre class="example-preformatted">'  this string starts and ends with whitespaces  '
</pre></div>

</li><li>Escaping and quoting can be mixed together:
<div class="example">
<pre class="example-preformatted">' The string '\'string\'' is a string '
</pre></div>

</li><li>To include a literal &lsquo;<samp class="samp">\</samp>&rsquo; you can use either escaping or quoting:
<div class="example">
<pre class="example-preformatted">'c:\foo' can be written as c:\\foo
</pre></div>
</li></ul>

<a class="anchor" id="date-syntax"></a><a name="Date"></a>
<h3 class="section">2.2 Date<span class="pull-right"><a class="anchor hidden-xs" href="#Date" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Date" aria-hidden="true">TOC</a></span></h3>

<p>The accepted syntax is:
</p><div class="example">
<pre class="example-preformatted">[(YYYY-MM-DD|YYYYMMDD)[T|t| ]]((HH:MM:SS[.m...]]])|(HHMMSS[.m...]]]))[Z]
now
</pre></div>

<p>If the value is &quot;now&quot; it takes the current time.
</p>
<p>Time is local time unless Z is appended, in which case it is
interpreted as UTC.
If the year-month-day part is not specified it takes the current
year-month-day.
</p>
<a class="anchor" id="time-duration-syntax"></a><a name="Time-duration"></a>
<h3 class="section">2.3 Time duration<span class="pull-right"><a class="anchor hidden-xs" href="#Time-duration" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Time-duration" aria-hidden="true">TOC</a></span></h3>

<p>There are two accepted syntaxes for expressing time duration.
</p>
<div class="example">
<pre class="example-preformatted">[-][<var class="var">HH</var>:]<var class="var">MM</var>:<var class="var">SS</var>[.<var class="var">m</var>...]
</pre></div>

<p><var class="var">HH</var> expresses the number of hours, <var class="var">MM</var> the number of minutes
for a maximum of 2 digits, and <var class="var">SS</var> the number of seconds for a
maximum of 2 digits. The <var class="var">m</var> at the end expresses decimal value for
<var class="var">SS</var>.
</p>
<p><em class="emph">or</em>
</p>
<div class="example">
<pre class="example-preformatted">[-]<var class="var">S</var>+[.<var class="var">m</var>...][s|ms|us]
</pre></div>

<p><var class="var">S</var> expresses the number of seconds, with the optional decimal part
<var class="var">m</var>.  The optional literal suffixes &lsquo;<samp class="samp">s</samp>&rsquo;, &lsquo;<samp class="samp">ms</samp>&rsquo; or &lsquo;<samp class="samp">us</samp>&rsquo;
indicate to interpret the value as seconds, milliseconds or microseconds,
respectively.
</p>
<p>In both expressions, the optional &lsquo;<samp class="samp">-</samp>&rsquo; indicates negative duration.
</p>
<a name="Examples-1"></a>
<h4 class="subsection">2.3.1 Examples<span class="pull-right"><a class="anchor hidden-xs" href="#Examples-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Examples-1" aria-hidden="true">TOC</a></span></h4>

<p>The following examples are all valid time duration:
</p>
<dl class="table">
<dt>&lsquo;<samp class="samp">55</samp>&rsquo;</dt>
<dd><p>55 seconds
</p>
</dd>
<dt>&lsquo;<samp class="samp">0.2</samp>&rsquo;</dt>
<dd><p>0.2 seconds
</p>
</dd>
<dt>&lsquo;<samp class="samp">200ms</samp>&rsquo;</dt>
<dd><p>200 milliseconds, that&rsquo;s 0.2s
</p>
</dd>
<dt>&lsquo;<samp class="samp">200000us</samp>&rsquo;</dt>
<dd><p>200000 microseconds, that&rsquo;s 0.2s
</p>
</dd>
<dt>&lsquo;<samp class="samp">12:03:45</samp>&rsquo;</dt>
<dd><p>12 hours, 03 minutes and 45 seconds
</p>
</dd>
<dt>&lsquo;<samp class="samp">23.189</samp>&rsquo;</dt>
<dd><p>23.189 seconds
</p></dd>
</dl>

<a class="anchor" id="video-size-syntax"></a><a name="Video-size"></a>
<h3 class="section">2.4 Video size<span class="pull-right"><a class="anchor hidden-xs" href="#Video-size" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Video-size" aria-hidden="true">TOC</a></span></h3>
<p>Specify the size of the sourced video, it may be a string of the form
<var class="var">width</var>x<var class="var">height</var>, or the name of a size abbreviation.
</p>
<p>The following abbreviations are recognized:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">ntsc</samp>&rsquo;</dt>
<dd><p>720x480
</p></dd>
<dt>&lsquo;<samp class="samp">pal</samp>&rsquo;</dt>
<dd><p>720x576
</p></dd>
<dt>&lsquo;<samp class="samp">qntsc</samp>&rsquo;</dt>
<dd><p>352x240
</p></dd>
<dt>&lsquo;<samp class="samp">qpal</samp>&rsquo;</dt>
<dd><p>352x288
</p></dd>
<dt>&lsquo;<samp class="samp">sntsc</samp>&rsquo;</dt>
<dd><p>640x480
</p></dd>
<dt>&lsquo;<samp class="samp">spal</samp>&rsquo;</dt>
<dd><p>768x576
</p></dd>
<dt>&lsquo;<samp class="samp">film</samp>&rsquo;</dt>
<dd><p>352x240
</p></dd>
<dt>&lsquo;<samp class="samp">ntsc-film</samp>&rsquo;</dt>
<dd><p>352x240
</p></dd>
<dt>&lsquo;<samp class="samp">sqcif</samp>&rsquo;</dt>
<dd><p>128x96
</p></dd>
<dt>&lsquo;<samp class="samp">qcif</samp>&rsquo;</dt>
<dd><p>176x144
</p></dd>
<dt>&lsquo;<samp class="samp">cif</samp>&rsquo;</dt>
<dd><p>352x288
</p></dd>
<dt>&lsquo;<samp class="samp">4cif</samp>&rsquo;</dt>
<dd><p>704x576
</p></dd>
<dt>&lsquo;<samp class="samp">16cif</samp>&rsquo;</dt>
<dd><p>1408x1152
</p></dd>
<dt>&lsquo;<samp class="samp">qqvga</samp>&rsquo;</dt>
<dd><p>160x120
</p></dd>
<dt>&lsquo;<samp class="samp">qvga</samp>&rsquo;</dt>
<dd><p>320x240
</p></dd>
<dt>&lsquo;<samp class="samp">vga</samp>&rsquo;</dt>
<dd><p>640x480
</p></dd>
<dt>&lsquo;<samp class="samp">svga</samp>&rsquo;</dt>
<dd><p>800x600
</p></dd>
<dt>&lsquo;<samp class="samp">xga</samp>&rsquo;</dt>
<dd><p>1024x768
</p></dd>
<dt>&lsquo;<samp class="samp">uxga</samp>&rsquo;</dt>
<dd><p>1600x1200
</p></dd>
<dt>&lsquo;<samp class="samp">qxga</samp>&rsquo;</dt>
<dd><p>2048x1536
</p></dd>
<dt>&lsquo;<samp class="samp">sxga</samp>&rsquo;</dt>
<dd><p>1280x1024
</p></dd>
<dt>&lsquo;<samp class="samp">qsxga</samp>&rsquo;</dt>
<dd><p>2560x2048
</p></dd>
<dt>&lsquo;<samp class="samp">hsxga</samp>&rsquo;</dt>
<dd><p>5120x4096
</p></dd>
<dt>&lsquo;<samp class="samp">wvga</samp>&rsquo;</dt>
<dd><p>852x480
</p></dd>
<dt>&lsquo;<samp class="samp">wxga</samp>&rsquo;</dt>
<dd><p>1366x768
</p></dd>
<dt>&lsquo;<samp class="samp">wsxga</samp>&rsquo;</dt>
<dd><p>1600x1024
</p></dd>
<dt>&lsquo;<samp class="samp">wuxga</samp>&rsquo;</dt>
<dd><p>1920x1200
</p></dd>
<dt>&lsquo;<samp class="samp">woxga</samp>&rsquo;</dt>
<dd><p>2560x1600
</p></dd>
<dt>&lsquo;<samp class="samp">wqsxga</samp>&rsquo;</dt>
<dd><p>3200x2048
</p></dd>
<dt>&lsquo;<samp class="samp">wquxga</samp>&rsquo;</dt>
<dd><p>3840x2400
</p></dd>
<dt>&lsquo;<samp class="samp">whsxga</samp>&rsquo;</dt>
<dd><p>6400x4096
</p></dd>
<dt>&lsquo;<samp class="samp">whuxga</samp>&rsquo;</dt>
<dd><p>7680x4800
</p></dd>
<dt>&lsquo;<samp class="samp">cga</samp>&rsquo;</dt>
<dd><p>320x200
</p></dd>
<dt>&lsquo;<samp class="samp">ega</samp>&rsquo;</dt>
<dd><p>640x350
</p></dd>
<dt>&lsquo;<samp class="samp">hd480</samp>&rsquo;</dt>
<dd><p>852x480
</p></dd>
<dt>&lsquo;<samp class="samp">hd720</samp>&rsquo;</dt>
<dd><p>1280x720
</p></dd>
<dt>&lsquo;<samp class="samp">hd1080</samp>&rsquo;</dt>
<dd><p>1920x1080
</p></dd>
<dt>&lsquo;<samp class="samp">2k</samp>&rsquo;</dt>
<dd><p>2048x1080
</p></dd>
<dt>&lsquo;<samp class="samp">2kflat</samp>&rsquo;</dt>
<dd><p>1998x1080
</p></dd>
<dt>&lsquo;<samp class="samp">2kscope</samp>&rsquo;</dt>
<dd><p>2048x858
</p></dd>
<dt>&lsquo;<samp class="samp">4k</samp>&rsquo;</dt>
<dd><p>4096x2160
</p></dd>
<dt>&lsquo;<samp class="samp">4kflat</samp>&rsquo;</dt>
<dd><p>3996x2160
</p></dd>
<dt>&lsquo;<samp class="samp">4kscope</samp>&rsquo;</dt>
<dd><p>4096x1716
</p></dd>
<dt>&lsquo;<samp class="samp">nhd</samp>&rsquo;</dt>
<dd><p>640x360
</p></dd>
<dt>&lsquo;<samp class="samp">hqvga</samp>&rsquo;</dt>
<dd><p>240x160
</p></dd>
<dt>&lsquo;<samp class="samp">wqvga</samp>&rsquo;</dt>
<dd><p>400x240
</p></dd>
<dt>&lsquo;<samp class="samp">fwqvga</samp>&rsquo;</dt>
<dd><p>432x240
</p></dd>
<dt>&lsquo;<samp class="samp">hvga</samp>&rsquo;</dt>
<dd><p>480x320
</p></dd>
<dt>&lsquo;<samp class="samp">qhd</samp>&rsquo;</dt>
<dd><p>960x540
</p></dd>
<dt>&lsquo;<samp class="samp">2kdci</samp>&rsquo;</dt>
<dd><p>2048x1080
</p></dd>
<dt>&lsquo;<samp class="samp">4kdci</samp>&rsquo;</dt>
<dd><p>4096x2160
</p></dd>
<dt>&lsquo;<samp class="samp">uhd2160</samp>&rsquo;</dt>
<dd><p>3840x2160
</p></dd>
<dt>&lsquo;<samp class="samp">uhd4320</samp>&rsquo;</dt>
<dd><p>7680x4320
</p></dd>
</dl>

<a class="anchor" id="video-rate-syntax"></a><a name="Video-rate"></a>
<h3 class="section">2.5 Video rate<span class="pull-right"><a class="anchor hidden-xs" href="#Video-rate" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Video-rate" aria-hidden="true">TOC</a></span></h3>

<p>Specify the frame rate of a video, expressed as the number of frames
generated per second. It has to be a string in the format
<var class="var">frame_rate_num</var>/<var class="var">frame_rate_den</var>, an integer number, a float
number or a valid video frame rate abbreviation.
</p>
<p>The following abbreviations are recognized:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">ntsc</samp>&rsquo;</dt>
<dd><p>30000/1001
</p></dd>
<dt>&lsquo;<samp class="samp">pal</samp>&rsquo;</dt>
<dd><p>25/1
</p></dd>
<dt>&lsquo;<samp class="samp">qntsc</samp>&rsquo;</dt>
<dd><p>30000/1001
</p></dd>
<dt>&lsquo;<samp class="samp">qpal</samp>&rsquo;</dt>
<dd><p>25/1
</p></dd>
<dt>&lsquo;<samp class="samp">sntsc</samp>&rsquo;</dt>
<dd><p>30000/1001
</p></dd>
<dt>&lsquo;<samp class="samp">spal</samp>&rsquo;</dt>
<dd><p>25/1
</p></dd>
<dt>&lsquo;<samp class="samp">film</samp>&rsquo;</dt>
<dd><p>24/1
</p></dd>
<dt>&lsquo;<samp class="samp">ntsc-film</samp>&rsquo;</dt>
<dd><p>24000/1001
</p></dd>
</dl>

<a class="anchor" id="ratio-syntax"></a><a name="Ratio"></a>
<h3 class="section">2.6 Ratio<span class="pull-right"><a class="anchor hidden-xs" href="#Ratio" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Ratio" aria-hidden="true">TOC</a></span></h3>

<p>A ratio can be expressed as an expression, or in the form
<var class="var">numerator</var>:<var class="var">denominator</var>.
</p>
<p>Note that a ratio with infinite (1/0) or negative value is
considered valid, so you should check on the returned value if you
want to exclude those values.
</p>
<p>The undefined value can be expressed using the &quot;0:0&quot; string.
</p>
<a class="anchor" id="color-syntax"></a><a name="Color"></a>
<h3 class="section">2.7 Color<span class="pull-right"><a class="anchor hidden-xs" href="#Color" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Color" aria-hidden="true">TOC</a></span></h3>

<p>It can be the name of a color as defined below (case insensitive match) or a
<code class="code">[0x|#]RRGGBB[AA]</code> sequence, possibly followed by @ and a string
representing the alpha component.
</p>
<p>The alpha component may be a string composed by &quot;0x&quot; followed by an
hexadecimal number or a decimal number between 0.0 and 1.0, which
represents the opacity value (&lsquo;<samp class="samp">0x00</samp>&rsquo; or &lsquo;<samp class="samp">0.0</samp>&rsquo; means completely
transparent, &lsquo;<samp class="samp">0xff</samp>&rsquo; or &lsquo;<samp class="samp">1.0</samp>&rsquo; completely opaque). If the alpha
component is not specified then &lsquo;<samp class="samp">0xff</samp>&rsquo; is assumed.
</p>
<p>The string &lsquo;<samp class="samp">random</samp>&rsquo; will result in a random color.
</p>
<p>The following names of colors are recognized:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">AliceBlue</samp>&rsquo;</dt>
<dd><p>0xF0F8FF
</p></dd>
<dt>&lsquo;<samp class="samp">AntiqueWhite</samp>&rsquo;</dt>
<dd><p>0xFAEBD7
</p></dd>
<dt>&lsquo;<samp class="samp">Aqua</samp>&rsquo;</dt>
<dd><p>0x00FFFF
</p></dd>
<dt>&lsquo;<samp class="samp">Aquamarine</samp>&rsquo;</dt>
<dd><p>0x7FFFD4
</p></dd>
<dt>&lsquo;<samp class="samp">Azure</samp>&rsquo;</dt>
<dd><p>0xF0FFFF
</p></dd>
<dt>&lsquo;<samp class="samp">Beige</samp>&rsquo;</dt>
<dd><p>0xF5F5DC
</p></dd>
<dt>&lsquo;<samp class="samp">Bisque</samp>&rsquo;</dt>
<dd><p>0xFFE4C4
</p></dd>
<dt>&lsquo;<samp class="samp">Black</samp>&rsquo;</dt>
<dd><p>0x000000
</p></dd>
<dt>&lsquo;<samp class="samp">BlanchedAlmond</samp>&rsquo;</dt>
<dd><p>0xFFEBCD
</p></dd>
<dt>&lsquo;<samp class="samp">Blue</samp>&rsquo;</dt>
<dd><p>0x0000FF
</p></dd>
<dt>&lsquo;<samp class="samp">BlueViolet</samp>&rsquo;</dt>
<dd><p>0x8A2BE2
</p></dd>
<dt>&lsquo;<samp class="samp">Brown</samp>&rsquo;</dt>
<dd><p>0xA52A2A
</p></dd>
<dt>&lsquo;<samp class="samp">BurlyWood</samp>&rsquo;</dt>
<dd><p>0xDEB887
</p></dd>
<dt>&lsquo;<samp class="samp">CadetBlue</samp>&rsquo;</dt>
<dd><p>0x5F9EA0
</p></dd>
<dt>&lsquo;<samp class="samp">Chartreuse</samp>&rsquo;</dt>
<dd><p>0x7FFF00
</p></dd>
<dt>&lsquo;<samp class="samp">Chocolate</samp>&rsquo;</dt>
<dd><p>0xD2691E
</p></dd>
<dt>&lsquo;<samp class="samp">Coral</samp>&rsquo;</dt>
<dd><p>0xFF7F50
</p></dd>
<dt>&lsquo;<samp class="samp">CornflowerBlue</samp>&rsquo;</dt>
<dd><p>0x6495ED
</p></dd>
<dt>&lsquo;<samp class="samp">Cornsilk</samp>&rsquo;</dt>
<dd><p>0xFFF8DC
</p></dd>
<dt>&lsquo;<samp class="samp">Crimson</samp>&rsquo;</dt>
<dd><p>0xDC143C
</p></dd>
<dt>&lsquo;<samp class="samp">Cyan</samp>&rsquo;</dt>
<dd><p>0x00FFFF
</p></dd>
<dt>&lsquo;<samp class="samp">DarkBlue</samp>&rsquo;</dt>
<dd><p>0x00008B
</p></dd>
<dt>&lsquo;<samp class="samp">DarkCyan</samp>&rsquo;</dt>
<dd><p>0x008B8B
</p></dd>
<dt>&lsquo;<samp class="samp">DarkGoldenRod</samp>&rsquo;</dt>
<dd><p>0xB8860B
</p></dd>
<dt>&lsquo;<samp class="samp">DarkGray</samp>&rsquo;</dt>
<dd><p>0xA9A9A9
</p></dd>
<dt>&lsquo;<samp class="samp">DarkGreen</samp>&rsquo;</dt>
<dd><p>0x006400
</p></dd>
<dt>&lsquo;<samp class="samp">DarkKhaki</samp>&rsquo;</dt>
<dd><p>0xBDB76B
</p></dd>
<dt>&lsquo;<samp class="samp">DarkMagenta</samp>&rsquo;</dt>
<dd><p>0x8B008B
</p></dd>
<dt>&lsquo;<samp class="samp">DarkOliveGreen</samp>&rsquo;</dt>
<dd><p>0x556B2F
</p></dd>
<dt>&lsquo;<samp class="samp">Darkorange</samp>&rsquo;</dt>
<dd><p>0xFF8C00
</p></dd>
<dt>&lsquo;<samp class="samp">DarkOrchid</samp>&rsquo;</dt>
<dd><p>0x9932CC
</p></dd>
<dt>&lsquo;<samp class="samp">DarkRed</samp>&rsquo;</dt>
<dd><p>0x8B0000
</p></dd>
<dt>&lsquo;<samp class="samp">DarkSalmon</samp>&rsquo;</dt>
<dd><p>0xE9967A
</p></dd>
<dt>&lsquo;<samp class="samp">DarkSeaGreen</samp>&rsquo;</dt>
<dd><p>0x8FBC8F
</p></dd>
<dt>&lsquo;<samp class="samp">DarkSlateBlue</samp>&rsquo;</dt>
<dd><p>0x483D8B
</p></dd>
<dt>&lsquo;<samp class="samp">DarkSlateGray</samp>&rsquo;</dt>
<dd><p>0x2F4F4F
</p></dd>
<dt>&lsquo;<samp class="samp">DarkTurquoise</samp>&rsquo;</dt>
<dd><p>0x00CED1
</p></dd>
<dt>&lsquo;<samp class="samp">DarkViolet</samp>&rsquo;</dt>
<dd><p>0x9400D3
</p></dd>
<dt>&lsquo;<samp class="samp">DeepPink</samp>&rsquo;</dt>
<dd><p>0xFF1493
</p></dd>
<dt>&lsquo;<samp class="samp">DeepSkyBlue</samp>&rsquo;</dt>
<dd><p>0x00BFFF
</p></dd>
<dt>&lsquo;<samp class="samp">DimGray</samp>&rsquo;</dt>
<dd><p>0x696969
</p></dd>
<dt>&lsquo;<samp class="samp">DodgerBlue</samp>&rsquo;</dt>
<dd><p>0x1E90FF
</p></dd>
<dt>&lsquo;<samp class="samp">FireBrick</samp>&rsquo;</dt>
<dd><p>0xB22222
</p></dd>
<dt>&lsquo;<samp class="samp">FloralWhite</samp>&rsquo;</dt>
<dd><p>0xFFFAF0
</p></dd>
<dt>&lsquo;<samp class="samp">ForestGreen</samp>&rsquo;</dt>
<dd><p>0x228B22
</p></dd>
<dt>&lsquo;<samp class="samp">Fuchsia</samp>&rsquo;</dt>
<dd><p>0xFF00FF
</p></dd>
<dt>&lsquo;<samp class="samp">Gainsboro</samp>&rsquo;</dt>
<dd><p>0xDCDCDC
</p></dd>
<dt>&lsquo;<samp class="samp">GhostWhite</samp>&rsquo;</dt>
<dd><p>0xF8F8FF
</p></dd>
<dt>&lsquo;<samp class="samp">Gold</samp>&rsquo;</dt>
<dd><p>0xFFD700
</p></dd>
<dt>&lsquo;<samp class="samp">GoldenRod</samp>&rsquo;</dt>
<dd><p>0xDAA520
</p></dd>
<dt>&lsquo;<samp class="samp">Gray</samp>&rsquo;</dt>
<dd><p>0x808080
</p></dd>
<dt>&lsquo;<samp class="samp">Green</samp>&rsquo;</dt>
<dd><p>0x008000
</p></dd>
<dt>&lsquo;<samp class="samp">GreenYellow</samp>&rsquo;</dt>
<dd><p>0xADFF2F
</p></dd>
<dt>&lsquo;<samp class="samp">HoneyDew</samp>&rsquo;</dt>
<dd><p>0xF0FFF0
</p></dd>
<dt>&lsquo;<samp class="samp">HotPink</samp>&rsquo;</dt>
<dd><p>0xFF69B4
</p></dd>
<dt>&lsquo;<samp class="samp">IndianRed</samp>&rsquo;</dt>
<dd><p>0xCD5C5C
</p></dd>
<dt>&lsquo;<samp class="samp">Indigo</samp>&rsquo;</dt>
<dd><p>0x4B0082
</p></dd>
<dt>&lsquo;<samp class="samp">Ivory</samp>&rsquo;</dt>
<dd><p>0xFFFFF0
</p></dd>
<dt>&lsquo;<samp class="samp">Khaki</samp>&rsquo;</dt>
<dd><p>0xF0E68C
</p></dd>
<dt>&lsquo;<samp class="samp">Lavender</samp>&rsquo;</dt>
<dd><p>0xE6E6FA
</p></dd>
<dt>&lsquo;<samp class="samp">LavenderBlush</samp>&rsquo;</dt>
<dd><p>0xFFF0F5
</p></dd>
<dt>&lsquo;<samp class="samp">LawnGreen</samp>&rsquo;</dt>
<dd><p>0x7CFC00
</p></dd>
<dt>&lsquo;<samp class="samp">LemonChiffon</samp>&rsquo;</dt>
<dd><p>0xFFFACD
</p></dd>
<dt>&lsquo;<samp class="samp">LightBlue</samp>&rsquo;</dt>
<dd><p>0xADD8E6
</p></dd>
<dt>&lsquo;<samp class="samp">LightCoral</samp>&rsquo;</dt>
<dd><p>0xF08080
</p></dd>
<dt>&lsquo;<samp class="samp">LightCyan</samp>&rsquo;</dt>
<dd><p>0xE0FFFF
</p></dd>
<dt>&lsquo;<samp class="samp">LightGoldenRodYellow</samp>&rsquo;</dt>
<dd><p>0xFAFAD2
</p></dd>
<dt>&lsquo;<samp class="samp">LightGreen</samp>&rsquo;</dt>
<dd><p>0x90EE90
</p></dd>
<dt>&lsquo;<samp class="samp">LightGrey</samp>&rsquo;</dt>
<dd><p>0xD3D3D3
</p></dd>
<dt>&lsquo;<samp class="samp">LightPink</samp>&rsquo;</dt>
<dd><p>0xFFB6C1
</p></dd>
<dt>&lsquo;<samp class="samp">LightSalmon</samp>&rsquo;</dt>
<dd><p>0xFFA07A
</p></dd>
<dt>&lsquo;<samp class="samp">LightSeaGreen</samp>&rsquo;</dt>
<dd><p>0x20B2AA
</p></dd>
<dt>&lsquo;<samp class="samp">LightSkyBlue</samp>&rsquo;</dt>
<dd><p>0x87CEFA
</p></dd>
<dt>&lsquo;<samp class="samp">LightSlateGray</samp>&rsquo;</dt>
<dd><p>0x778899
</p></dd>
<dt>&lsquo;<samp class="samp">LightSteelBlue</samp>&rsquo;</dt>
<dd><p>0xB0C4DE
</p></dd>
<dt>&lsquo;<samp class="samp">LightYellow</samp>&rsquo;</dt>
<dd><p>0xFFFFE0
</p></dd>
<dt>&lsquo;<samp class="samp">Lime</samp>&rsquo;</dt>
<dd><p>0x00FF00
</p></dd>
<dt>&lsquo;<samp class="samp">LimeGreen</samp>&rsquo;</dt>
<dd><p>0x32CD32
</p></dd>
<dt>&lsquo;<samp class="samp">Linen</samp>&rsquo;</dt>
<dd><p>0xFAF0E6
</p></dd>
<dt>&lsquo;<samp class="samp">Magenta</samp>&rsquo;</dt>
<dd><p>0xFF00FF
</p></dd>
<dt>&lsquo;<samp class="samp">Maroon</samp>&rsquo;</dt>
<dd><p>0x800000
</p></dd>
<dt>&lsquo;<samp class="samp">MediumAquaMarine</samp>&rsquo;</dt>
<dd><p>0x66CDAA
</p></dd>
<dt>&lsquo;<samp class="samp">MediumBlue</samp>&rsquo;</dt>
<dd><p>0x0000CD
</p></dd>
<dt>&lsquo;<samp class="samp">MediumOrchid</samp>&rsquo;</dt>
<dd><p>0xBA55D3
</p></dd>
<dt>&lsquo;<samp class="samp">MediumPurple</samp>&rsquo;</dt>
<dd><p>0x9370D8
</p></dd>
<dt>&lsquo;<samp class="samp">MediumSeaGreen</samp>&rsquo;</dt>
<dd><p>0x3CB371
</p></dd>
<dt>&lsquo;<samp class="samp">MediumSlateBlue</samp>&rsquo;</dt>
<dd><p>0x7B68EE
</p></dd>
<dt>&lsquo;<samp class="samp">MediumSpringGreen</samp>&rsquo;</dt>
<dd><p>0x00FA9A
</p></dd>
<dt>&lsquo;<samp class="samp">MediumTurquoise</samp>&rsquo;</dt>
<dd><p>0x48D1CC
</p></dd>
<dt>&lsquo;<samp class="samp">MediumVioletRed</samp>&rsquo;</dt>
<dd><p>0xC71585
</p></dd>
<dt>&lsquo;<samp class="samp">MidnightBlue</samp>&rsquo;</dt>
<dd><p>0x191970
</p></dd>
<dt>&lsquo;<samp class="samp">MintCream</samp>&rsquo;</dt>
<dd><p>0xF5FFFA
</p></dd>
<dt>&lsquo;<samp class="samp">MistyRose</samp>&rsquo;</dt>
<dd><p>0xFFE4E1
</p></dd>
<dt>&lsquo;<samp class="samp">Moccasin</samp>&rsquo;</dt>
<dd><p>0xFFE4B5
</p></dd>
<dt>&lsquo;<samp class="samp">NavajoWhite</samp>&rsquo;</dt>
<dd><p>0xFFDEAD
</p></dd>
<dt>&lsquo;<samp class="samp">Navy</samp>&rsquo;</dt>
<dd><p>0x000080
</p></dd>
<dt>&lsquo;<samp class="samp">OldLace</samp>&rsquo;</dt>
<dd><p>0xFDF5E6
</p></dd>
<dt>&lsquo;<samp class="samp">Olive</samp>&rsquo;</dt>
<dd><p>0x808000
</p></dd>
<dt>&lsquo;<samp class="samp">OliveDrab</samp>&rsquo;</dt>
<dd><p>0x6B8E23
</p></dd>
<dt>&lsquo;<samp class="samp">Orange</samp>&rsquo;</dt>
<dd><p>0xFFA500
</p></dd>
<dt>&lsquo;<samp class="samp">OrangeRed</samp>&rsquo;</dt>
<dd><p>0xFF4500
</p></dd>
<dt>&lsquo;<samp class="samp">Orchid</samp>&rsquo;</dt>
<dd><p>0xDA70D6
</p></dd>
<dt>&lsquo;<samp class="samp">PaleGoldenRod</samp>&rsquo;</dt>
<dd><p>0xEEE8AA
</p></dd>
<dt>&lsquo;<samp class="samp">PaleGreen</samp>&rsquo;</dt>
<dd><p>0x98FB98
</p></dd>
<dt>&lsquo;<samp class="samp">PaleTurquoise</samp>&rsquo;</dt>
<dd><p>0xAFEEEE
</p></dd>
<dt>&lsquo;<samp class="samp">PaleVioletRed</samp>&rsquo;</dt>
<dd><p>0xD87093
</p></dd>
<dt>&lsquo;<samp class="samp">PapayaWhip</samp>&rsquo;</dt>
<dd><p>0xFFEFD5
</p></dd>
<dt>&lsquo;<samp class="samp">PeachPuff</samp>&rsquo;</dt>
<dd><p>0xFFDAB9
</p></dd>
<dt>&lsquo;<samp class="samp">Peru</samp>&rsquo;</dt>
<dd><p>0xCD853F
</p></dd>
<dt>&lsquo;<samp class="samp">Pink</samp>&rsquo;</dt>
<dd><p>0xFFC0CB
</p></dd>
<dt>&lsquo;<samp class="samp">Plum</samp>&rsquo;</dt>
<dd><p>0xDDA0DD
</p></dd>
<dt>&lsquo;<samp class="samp">PowderBlue</samp>&rsquo;</dt>
<dd><p>0xB0E0E6
</p></dd>
<dt>&lsquo;<samp class="samp">Purple</samp>&rsquo;</dt>
<dd><p>0x800080
</p></dd>
<dt>&lsquo;<samp class="samp">Red</samp>&rsquo;</dt>
<dd><p>0xFF0000
</p></dd>
<dt>&lsquo;<samp class="samp">RosyBrown</samp>&rsquo;</dt>
<dd><p>0xBC8F8F
</p></dd>
<dt>&lsquo;<samp class="samp">RoyalBlue</samp>&rsquo;</dt>
<dd><p>0x4169E1
</p></dd>
<dt>&lsquo;<samp class="samp">SaddleBrown</samp>&rsquo;</dt>
<dd><p>0x8B4513
</p></dd>
<dt>&lsquo;<samp class="samp">Salmon</samp>&rsquo;</dt>
<dd><p>0xFA8072
</p></dd>
<dt>&lsquo;<samp class="samp">SandyBrown</samp>&rsquo;</dt>
<dd><p>0xF4A460
</p></dd>
<dt>&lsquo;<samp class="samp">SeaGreen</samp>&rsquo;</dt>
<dd><p>0x2E8B57
</p></dd>
<dt>&lsquo;<samp class="samp">SeaShell</samp>&rsquo;</dt>
<dd><p>0xFFF5EE
</p></dd>
<dt>&lsquo;<samp class="samp">Sienna</samp>&rsquo;</dt>
<dd><p>0xA0522D
</p></dd>
<dt>&lsquo;<samp class="samp">Silver</samp>&rsquo;</dt>
<dd><p>0xC0C0C0
</p></dd>
<dt>&lsquo;<samp class="samp">SkyBlue</samp>&rsquo;</dt>
<dd><p>0x87CEEB
</p></dd>
<dt>&lsquo;<samp class="samp">SlateBlue</samp>&rsquo;</dt>
<dd><p>0x6A5ACD
</p></dd>
<dt>&lsquo;<samp class="samp">SlateGray</samp>&rsquo;</dt>
<dd><p>0x708090
</p></dd>
<dt>&lsquo;<samp class="samp">Snow</samp>&rsquo;</dt>
<dd><p>0xFFFAFA
</p></dd>
<dt>&lsquo;<samp class="samp">SpringGreen</samp>&rsquo;</dt>
<dd><p>0x00FF7F
</p></dd>
<dt>&lsquo;<samp class="samp">SteelBlue</samp>&rsquo;</dt>
<dd><p>0x4682B4
</p></dd>
<dt>&lsquo;<samp class="samp">Tan</samp>&rsquo;</dt>
<dd><p>0xD2B48C
</p></dd>
<dt>&lsquo;<samp class="samp">Teal</samp>&rsquo;</dt>
<dd><p>0x008080
</p></dd>
<dt>&lsquo;<samp class="samp">Thistle</samp>&rsquo;</dt>
<dd><p>0xD8BFD8
</p></dd>
<dt>&lsquo;<samp class="samp">Tomato</samp>&rsquo;</dt>
<dd><p>0xFF6347
</p></dd>
<dt>&lsquo;<samp class="samp">Turquoise</samp>&rsquo;</dt>
<dd><p>0x40E0D0
</p></dd>
<dt>&lsquo;<samp class="samp">Violet</samp>&rsquo;</dt>
<dd><p>0xEE82EE
</p></dd>
<dt>&lsquo;<samp class="samp">Wheat</samp>&rsquo;</dt>
<dd><p>0xF5DEB3
</p></dd>
<dt>&lsquo;<samp class="samp">White</samp>&rsquo;</dt>
<dd><p>0xFFFFFF
</p></dd>
<dt>&lsquo;<samp class="samp">WhiteSmoke</samp>&rsquo;</dt>
<dd><p>0xF5F5F5
</p></dd>
<dt>&lsquo;<samp class="samp">Yellow</samp>&rsquo;</dt>
<dd><p>0xFFFF00
</p></dd>
<dt>&lsquo;<samp class="samp">YellowGreen</samp>&rsquo;</dt>
<dd><p>0x9ACD32
</p></dd>
</dl>

<a class="anchor" id="channel-layout-syntax"></a><a name="Channel-Layout"></a>
<h3 class="section">2.8 Channel Layout<span class="pull-right"><a class="anchor hidden-xs" href="#Channel-Layout" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Channel-Layout" aria-hidden="true">TOC</a></span></h3>

<p>A channel layout specifies the spatial disposition of the channels in
a multi-channel audio stream. To specify a channel layout, FFmpeg
makes use of a special syntax.
</p>
<p>Individual channels are identified by an id, as given by the table
below:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">FL</samp>&rsquo;</dt>
<dd><p>front left
</p></dd>
<dt>&lsquo;<samp class="samp">FR</samp>&rsquo;</dt>
<dd><p>front right
</p></dd>
<dt>&lsquo;<samp class="samp">FC</samp>&rsquo;</dt>
<dd><p>front center
</p></dd>
<dt>&lsquo;<samp class="samp">LFE</samp>&rsquo;</dt>
<dd><p>low frequency
</p></dd>
<dt>&lsquo;<samp class="samp">BL</samp>&rsquo;</dt>
<dd><p>back left
</p></dd>
<dt>&lsquo;<samp class="samp">BR</samp>&rsquo;</dt>
<dd><p>back right
</p></dd>
<dt>&lsquo;<samp class="samp">FLC</samp>&rsquo;</dt>
<dd><p>front left-of-center
</p></dd>
<dt>&lsquo;<samp class="samp">FRC</samp>&rsquo;</dt>
<dd><p>front right-of-center
</p></dd>
<dt>&lsquo;<samp class="samp">BC</samp>&rsquo;</dt>
<dd><p>back center
</p></dd>
<dt>&lsquo;<samp class="samp">SL</samp>&rsquo;</dt>
<dd><p>side left
</p></dd>
<dt>&lsquo;<samp class="samp">SR</samp>&rsquo;</dt>
<dd><p>side right
</p></dd>
<dt>&lsquo;<samp class="samp">TC</samp>&rsquo;</dt>
<dd><p>top center
</p></dd>
<dt>&lsquo;<samp class="samp">TFL</samp>&rsquo;</dt>
<dd><p>top front left
</p></dd>
<dt>&lsquo;<samp class="samp">TFC</samp>&rsquo;</dt>
<dd><p>top front center
</p></dd>
<dt>&lsquo;<samp class="samp">TFR</samp>&rsquo;</dt>
<dd><p>top front right
</p></dd>
<dt>&lsquo;<samp class="samp">TBL</samp>&rsquo;</dt>
<dd><p>top back left
</p></dd>
<dt>&lsquo;<samp class="samp">TBC</samp>&rsquo;</dt>
<dd><p>top back center
</p></dd>
<dt>&lsquo;<samp class="samp">TBR</samp>&rsquo;</dt>
<dd><p>top back right
</p></dd>
<dt>&lsquo;<samp class="samp">DL</samp>&rsquo;</dt>
<dd><p>downmix left
</p></dd>
<dt>&lsquo;<samp class="samp">DR</samp>&rsquo;</dt>
<dd><p>downmix right
</p></dd>
<dt>&lsquo;<samp class="samp">WL</samp>&rsquo;</dt>
<dd><p>wide left
</p></dd>
<dt>&lsquo;<samp class="samp">WR</samp>&rsquo;</dt>
<dd><p>wide right
</p></dd>
<dt>&lsquo;<samp class="samp">SDL</samp>&rsquo;</dt>
<dd><p>surround direct left
</p></dd>
<dt>&lsquo;<samp class="samp">SDR</samp>&rsquo;</dt>
<dd><p>surround direct right
</p></dd>
<dt>&lsquo;<samp class="samp">LFE2</samp>&rsquo;</dt>
<dd><p>low frequency 2
</p></dd>
</dl>

<p>Standard channel layout compositions can be specified by using the
following identifiers:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">mono</samp>&rsquo;</dt>
<dd><p>FC
</p></dd>
<dt>&lsquo;<samp class="samp">stereo</samp>&rsquo;</dt>
<dd><p>FL+FR
</p></dd>
<dt>&lsquo;<samp class="samp">2.1</samp>&rsquo;</dt>
<dd><p>FL+FR+LFE
</p></dd>
<dt>&lsquo;<samp class="samp">3.0</samp>&rsquo;</dt>
<dd><p>FL+FR+FC
</p></dd>
<dt>&lsquo;<samp class="samp">3.0(back)</samp>&rsquo;</dt>
<dd><p>FL+FR+BC
</p></dd>
<dt>&lsquo;<samp class="samp">4.0</samp>&rsquo;</dt>
<dd><p>FL+FR+FC+BC
</p></dd>
<dt>&lsquo;<samp class="samp">quad</samp>&rsquo;</dt>
<dd><p>FL+FR+BL+BR
</p></dd>
<dt>&lsquo;<samp class="samp">quad(side)</samp>&rsquo;</dt>
<dd><p>FL+FR+SL+SR
</p></dd>
<dt>&lsquo;<samp class="samp">3.1</samp>&rsquo;</dt>
<dd><p>FL+FR+FC+LFE
</p></dd>
<dt>&lsquo;<samp class="samp">5.0</samp>&rsquo;</dt>
<dd><p>FL+FR+FC+BL+BR
</p></dd>
<dt>&lsquo;<samp class="samp">5.0(side)</samp>&rsquo;</dt>
<dd><p>FL+FR+FC+SL+SR
</p></dd>
<dt>&lsquo;<samp class="samp">4.1</samp>&rsquo;</dt>
<dd><p>FL+FR+FC+LFE+BC
</p></dd>
<dt>&lsquo;<samp class="samp">5.1</samp>&rsquo;</dt>
<dd><p>FL+FR+FC+LFE+BL+BR
</p></dd>
<dt>&lsquo;<samp class="samp">5.1(side)</samp>&rsquo;</dt>
<dd><p>FL+FR+FC+LFE+SL+SR
</p></dd>
<dt>&lsquo;<samp class="samp">6.0</samp>&rsquo;</dt>
<dd><p>FL+FR+FC+BC+SL+SR
</p></dd>
<dt>&lsquo;<samp class="samp">6.0(front)</samp>&rsquo;</dt>
<dd><p>FL+FR+FLC+FRC+SL+SR
</p></dd>
<dt>&lsquo;<samp class="samp">3.1.2</samp>&rsquo;</dt>
<dd><p>FL+FR+FC+LFE+TFL+TFR
</p></dd>
<dt>&lsquo;<samp class="samp">hexagonal</samp>&rsquo;</dt>
<dd><p>FL+FR+FC+BL+BR+BC
</p></dd>
<dt>&lsquo;<samp class="samp">6.1</samp>&rsquo;</dt>
<dd><p>FL+FR+FC+LFE+BC+SL+SR
</p></dd>
<dt>&lsquo;<samp class="samp">6.1</samp>&rsquo;</dt>
<dd><p>FL+FR+FC+LFE+BL+BR+BC
</p></dd>
<dt>&lsquo;<samp class="samp">6.1(front)</samp>&rsquo;</dt>
<dd><p>FL+FR+LFE+FLC+FRC+SL+SR
</p></dd>
<dt>&lsquo;<samp class="samp">7.0</samp>&rsquo;</dt>
<dd><p>FL+FR+FC+BL+BR+SL+SR
</p></dd>
<dt>&lsquo;<samp class="samp">7.0(front)</samp>&rsquo;</dt>
<dd><p>FL+FR+FC+FLC+FRC+SL+SR
</p></dd>
<dt>&lsquo;<samp class="samp">7.1</samp>&rsquo;</dt>
<dd><p>FL+FR+FC+LFE+BL+BR+SL+SR
</p></dd>
<dt>&lsquo;<samp class="samp">7.1(wide)</samp>&rsquo;</dt>
<dd><p>FL+FR+FC+LFE+BL+BR+FLC+FRC
</p></dd>
<dt>&lsquo;<samp class="samp">7.1(wide-side)</samp>&rsquo;</dt>
<dd><p>FL+FR+FC+LFE+FLC+FRC+SL+SR
</p></dd>
<dt>&lsquo;<samp class="samp">5.1.2</samp>&rsquo;</dt>
<dd><p>FL+FR+FC+LFE+BL+BR+TFL+TFR
</p></dd>
<dt>&lsquo;<samp class="samp">octagonal</samp>&rsquo;</dt>
<dd><p>FL+FR+FC+BL+BR+BC+SL+SR
</p></dd>
<dt>&lsquo;<samp class="samp">cube</samp>&rsquo;</dt>
<dd><p>FL+FR+BL+BR+TFL+TFR+TBL+TBR
</p></dd>
<dt>&lsquo;<samp class="samp">5.1.4</samp>&rsquo;</dt>
<dd><p>FL+FR+FC+LFE+BL+BR+TFL+TFR+TBL+TBR
</p></dd>
<dt>&lsquo;<samp class="samp">7.1.2</samp>&rsquo;</dt>
<dd><p>FL+FR+FC+LFE+BL+BR+SL+SR+TFL+TFR
</p></dd>
<dt>&lsquo;<samp class="samp">7.1.4</samp>&rsquo;</dt>
<dd><p>FL+FR+FC+LFE+BL+BR+SL+SR+TFL+TFR+TBL+TBR
</p></dd>
<dt>&lsquo;<samp class="samp">7.2.3</samp>&rsquo;</dt>
<dd><p>FL+FR+FC+LFE+BL+BR+SL+SR+TFL+TFR+TBC+LFE2
</p></dd>
<dt>&lsquo;<samp class="samp">9.1.4</samp>&rsquo;</dt>
<dd><p>FL+FR+FC+LFE+BL+BR+FLC+FRC+SL+SR+TFL+TFR+TBL+TBR
</p></dd>
<dt>&lsquo;<samp class="samp">9.1.6</samp>&rsquo;</dt>
<dd><p>FL+FR+FC+LFE+BL+BR+FLC+FRC+SL+SR+TFL+TFR+TBL+TBR+TSL+TSR
</p></dd>
<dt>&lsquo;<samp class="samp">hexadecagonal</samp>&rsquo;</dt>
<dd><p>FL+FR+FC+BL+BR+BC+SL+SR+WL+WR+TBL+TBR+TBC+TFC+TFL+TFR
</p></dd>
<dt>&lsquo;<samp class="samp">binaural</samp>&rsquo;</dt>
<dd><p>BIL+BIR
</p></dd>
<dt>&lsquo;<samp class="samp">downmix</samp>&rsquo;</dt>
<dd><p>DL+DR
</p></dd>
<dt>&lsquo;<samp class="samp">22.2</samp>&rsquo;</dt>
<dd><p>FL+FR+FC+LFE+BL+BR+FLC+FRC+BC+SL+SR+TC+TFL+TFC+TFR+TBL+TBC+TBR+LFE2+TSL+TSR+BFC+BFL+BFR
</p></dd>
</dl>

<p>A custom channel layout can be specified as a sequence of terms, separated by &rsquo;+&rsquo;.
Each term can be:
</p><ul class="itemize mark-bullet">
<li>the name of a single channel (e.g. &lsquo;<samp class="samp">FL</samp>&rsquo;, &lsquo;<samp class="samp">FR</samp>&rsquo;, &lsquo;<samp class="samp">FC</samp>&rsquo;, &lsquo;<samp class="samp">LFE</samp>&rsquo;, etc.),
each optionally containing a custom name after a &rsquo;@&rsquo;, (e.g. &lsquo;<samp class="samp">FL@Left</samp>&rsquo;,
&lsquo;<samp class="samp">FR@Right</samp>&rsquo;, &lsquo;<samp class="samp">FC@Center</samp>&rsquo;, &lsquo;<samp class="samp">LFE@Low_Frequency</samp>&rsquo;, etc.)
</li></ul>

<p>A standard channel layout can be specified by the following:
</p><ul class="itemize mark-bullet">
<li>the name of a single channel (e.g. &lsquo;<samp class="samp">FL</samp>&rsquo;, &lsquo;<samp class="samp">FR</samp>&rsquo;, &lsquo;<samp class="samp">FC</samp>&rsquo;, &lsquo;<samp class="samp">LFE</samp>&rsquo;, etc.)

</li><li>the name of a standard channel layout (e.g. &lsquo;<samp class="samp">mono</samp>&rsquo;,
&lsquo;<samp class="samp">stereo</samp>&rsquo;, &lsquo;<samp class="samp">4.0</samp>&rsquo;, &lsquo;<samp class="samp">quad</samp>&rsquo;, &lsquo;<samp class="samp">5.0</samp>&rsquo;, etc.)

</li><li>a number of channels, in decimal, followed by &rsquo;c&rsquo;, yielding the default channel
layout for that number of channels (see the function
<code class="code">av_channel_layout_default</code>). Note that not all channel counts have a
default layout.

</li><li>a number of channels, in decimal, followed by &rsquo;C&rsquo;, yielding an unknown channel
layout with the specified number of channels. Note that not all channel layout
specification strings support unknown channel layouts.

</li><li>a channel layout mask, in hexadecimal starting with &quot;0x&quot; (see the
<code class="code">AV_CH_*</code> macros in <samp class="file">libavutil/channel_layout.h</samp>.
</li></ul>

<p>Before libavutil version 53 the trailing character &quot;c&quot; to specify a number of
channels was optional, but now it is required, while a channel layout mask can
also be specified as a decimal number (if and only if not followed by &quot;c&quot; or &quot;C&quot;).
</p>
<p>See also the function <code class="code">av_channel_layout_from_string</code> defined in
<samp class="file">libavutil/channel_layout.h</samp>.
</p>
<a name="Expression-Evaluation"></a>
<h2 class="chapter">3 Expression Evaluation<span class="pull-right"><a class="anchor hidden-xs" href="#Expression-Evaluation" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Expression-Evaluation" aria-hidden="true">TOC</a></span></h2>

<p>When evaluating an arithmetic expression, FFmpeg uses an internal
formula evaluator, implemented through the <samp class="file">libavutil/eval.h</samp>
interface.
</p>
<p>An expression may contain unary, binary operators, constants, and
functions.
</p>
<p>Two expressions <var class="var">expr1</var> and <var class="var">expr2</var> can be combined to form
another expression &quot;<var class="var">expr1</var>;<var class="var">expr2</var>&quot;.
<var class="var">expr1</var> and <var class="var">expr2</var> are evaluated in turn, and the new
expression evaluates to the value of <var class="var">expr2</var>.
</p>
<p>The following binary operators are available: <code class="code">+</code>, <code class="code">-</code>,
<code class="code">*</code>, <code class="code">/</code>, <code class="code">^</code>.
</p>
<p>The following unary operators are available: <code class="code">+</code>, <code class="code">-</code>.
</p>
<p>Some internal variables can be used to store and load intermediary
results. They can be accessed using the <code class="code">ld</code> and <code class="code">st</code>
functions with an index argument varying from 0 to 9 to specify which
internal variable to access.
</p>
<p>The following functions are available:
</p><dl class="table">
<dt><samp class="option">abs(x)</samp></dt>
<dd><p>Compute absolute value of <var class="var">x</var>.
</p>
</dd>
<dt><samp class="option">acos(x)</samp></dt>
<dd><p>Compute arccosine of <var class="var">x</var>.
</p>
</dd>
<dt><samp class="option">asin(x)</samp></dt>
<dd><p>Compute arcsine of <var class="var">x</var>.
</p>
</dd>
<dt><samp class="option">atan(x)</samp></dt>
<dd><p>Compute arctangent of <var class="var">x</var>.
</p>
</dd>
<dt><samp class="option">atan2(y, x)</samp></dt>
<dd><p>Compute principal value of the arc tangent of <var class="var">y</var>/<var class="var">x</var>.
</p>
</dd>
<dt><samp class="option">between(x, min, max)</samp></dt>
<dd><p>Return 1 if <var class="var">x</var> is greater than or equal to <var class="var">min</var> and lesser than or
equal to <var class="var">max</var>, 0 otherwise.
</p>
</dd>
<dt><samp class="option">bitand(x, y)</samp></dt>
<dt><samp class="option">bitor(x, y)</samp></dt>
<dd><p>Compute bitwise and/or operation on <var class="var">x</var> and <var class="var">y</var>.
</p>
<p>The results of the evaluation of <var class="var">x</var> and <var class="var">y</var> are converted to
integers before executing the bitwise operation.
</p>
<p>Note that both the conversion to integer and the conversion back to
floating point can lose precision. Beware of unexpected results for
large numbers (usually 2^53 and larger).
</p>
</dd>
<dt><samp class="option">ceil(expr)</samp></dt>
<dd><p>Round the value of expression <var class="var">expr</var> upwards to the nearest
integer. For example, &quot;ceil(1.5)&quot; is &quot;2.0&quot;.
</p>
</dd>
<dt><samp class="option">clip(x, min, max)</samp></dt>
<dd><p>Return the value of <var class="var">x</var> clipped between <var class="var">min</var> and <var class="var">max</var>.
</p>
</dd>
<dt><samp class="option">cos(x)</samp></dt>
<dd><p>Compute cosine of <var class="var">x</var>.
</p>
</dd>
<dt><samp class="option">cosh(x)</samp></dt>
<dd><p>Compute hyperbolic cosine of <var class="var">x</var>.
</p>
</dd>
<dt><samp class="option">eq(x, y)</samp></dt>
<dd><p>Return 1 if <var class="var">x</var> and <var class="var">y</var> are equivalent, 0 otherwise.
</p>
</dd>
<dt><samp class="option">exp(x)</samp></dt>
<dd><p>Compute exponential of <var class="var">x</var> (with base <code class="code">e</code>, the Euler&rsquo;s number).
</p>
</dd>
<dt><samp class="option">floor(expr)</samp></dt>
<dd><p>Round the value of expression <var class="var">expr</var> downwards to the nearest
integer. For example, &quot;floor(-1.5)&quot; is &quot;-2.0&quot;.
</p>
</dd>
<dt><samp class="option">gauss(x)</samp></dt>
<dd><p>Compute Gauss function of <var class="var">x</var>, corresponding to
<code class="code">exp(-x*x/2) / sqrt(2*PI)</code>.
</p>
</dd>
<dt><samp class="option">gcd(x, y)</samp></dt>
<dd><p>Return the greatest common divisor of <var class="var">x</var> and <var class="var">y</var>. If both <var class="var">x</var> and
<var class="var">y</var> are 0 or either or both are less than zero then behavior is undefined.
</p>
</dd>
<dt><samp class="option">gt(x, y)</samp></dt>
<dd><p>Return 1 if <var class="var">x</var> is greater than <var class="var">y</var>, 0 otherwise.
</p>
</dd>
<dt><samp class="option">gte(x, y)</samp></dt>
<dd><p>Return 1 if <var class="var">x</var> is greater than or equal to <var class="var">y</var>, 0 otherwise.
</p>
</dd>
<dt><samp class="option">hypot(x, y)</samp></dt>
<dd><p>This function is similar to the C function with the same name; it returns
&quot;sqrt(<var class="var">x</var>*<var class="var">x</var> + <var class="var">y</var>*<var class="var">y</var>)&quot;, the length of the hypotenuse of a
right triangle with sides of length <var class="var">x</var> and <var class="var">y</var>, or the distance of the
point (<var class="var">x</var>, <var class="var">y</var>) from the origin.
</p>
</dd>
<dt><samp class="option">if(x, y)</samp></dt>
<dd><p>Evaluate <var class="var">x</var>, and if the result is non-zero return the result of
the evaluation of <var class="var">y</var>, return 0 otherwise.
</p>
</dd>
<dt><samp class="option">if(x, y, z)</samp></dt>
<dd><p>Evaluate <var class="var">x</var>, and if the result is non-zero return the evaluation
result of <var class="var">y</var>, otherwise the evaluation result of <var class="var">z</var>.
</p>
</dd>
<dt><samp class="option">ifnot(x, y)</samp></dt>
<dd><p>Evaluate <var class="var">x</var>, and if the result is zero return the result of the
evaluation of <var class="var">y</var>, return 0 otherwise.
</p>
</dd>
<dt><samp class="option">ifnot(x, y, z)</samp></dt>
<dd><p>Evaluate <var class="var">x</var>, and if the result is zero return the evaluation
result of <var class="var">y</var>, otherwise the evaluation result of <var class="var">z</var>.
</p>
</dd>
<dt><samp class="option">isinf(x)</samp></dt>
<dd><p>Return 1.0 if <var class="var">x</var> is +/-INFINITY, 0.0 otherwise.
</p>
</dd>
<dt><samp class="option">isnan(x)</samp></dt>
<dd><p>Return 1.0 if <var class="var">x</var> is NAN, 0.0 otherwise.
</p>
</dd>
<dt><samp class="option">ld(idx)</samp></dt>
<dd><p>Load the value of the internal variable with index <var class="var">idx</var>, which was
previously stored with st(<var class="var">idx</var>, <var class="var">expr</var>).
The function returns the loaded value.
</p>
</dd>
<dt><samp class="option">lerp(x, y, z)</samp></dt>
<dd><p>Return linear interpolation between <var class="var">x</var> and <var class="var">y</var> by amount of <var class="var">z</var>.
</p>
</dd>
<dt><samp class="option">log(x)</samp></dt>
<dd><p>Compute natural logarithm of <var class="var">x</var>.
</p>
</dd>
<dt><samp class="option">lt(x, y)</samp></dt>
<dd><p>Return 1 if <var class="var">x</var> is lesser than <var class="var">y</var>, 0 otherwise.
</p>
</dd>
<dt><samp class="option">lte(x, y)</samp></dt>
<dd><p>Return 1 if <var class="var">x</var> is lesser than or equal to <var class="var">y</var>, 0 otherwise.
</p>
</dd>
<dt><samp class="option">max(x, y)</samp></dt>
<dd><p>Return the maximum between <var class="var">x</var> and <var class="var">y</var>.
</p>
</dd>
<dt><samp class="option">min(x, y)</samp></dt>
<dd><p>Return the minimum between <var class="var">x</var> and <var class="var">y</var>.
</p>
</dd>
<dt><samp class="option">mod(x, y)</samp></dt>
<dd><p>Compute the remainder of division of <var class="var">x</var> by <var class="var">y</var>.
</p>
</dd>
<dt><samp class="option">not(expr)</samp></dt>
<dd><p>Return 1.0 if <var class="var">expr</var> is zero, 0.0 otherwise.
</p>
</dd>
<dt><samp class="option">pow(x, y)</samp></dt>
<dd><p>Compute the power of <var class="var">x</var> elevated <var class="var">y</var>, it is equivalent to
&quot;(<var class="var">x</var>)^(<var class="var">y</var>)&quot;.
</p>
</dd>
<dt><samp class="option">print(t)</samp></dt>
<dt><samp class="option">print(t, l)</samp></dt>
<dd><p>Print the value of expression <var class="var">t</var> with loglevel <var class="var">l</var>. If <var class="var">l</var> is not
specified then a default log level is used.
Return the value of the expression printed.
</p>
</dd>
<dt><samp class="option">random(idx)</samp></dt>
<dd><p>Return a pseudo random value between 0.0 and 1.0. <var class="var">idx</var> is the
index of the internal variable used to save the seed/state, which can be
previously stored with <code class="code">st(idx)</code>.
</p>
<p>To initialize the seed, you need to store the seed value as a 64-bit
unsigned integer in the internal variable with index <var class="var">idx</var>.
</p>
<p>For example, to store the seed with value <code class="code">42</code> in the internal
variable with index <code class="code">0</code> and print a few random values:
</p><div class="example">
<pre class="example-preformatted">st(0,42); print(random(0)); print(random(0)); print(random(0))
</pre></div>

</dd>
<dt><samp class="option">randomi(idx, min, max)</samp></dt>
<dd><p>Return a pseudo random value in the interval between <var class="var">min</var> and
<var class="var">max</var>. <var class="var">idx</var> is the index of the internal variable which will be used to
save the seed/state, which can be previously stored with <code class="code">st(idx)</code>.
</p>
<p>To initialize the seed, you need to store the seed value as a 64-bit
unsigned integer in the internal variable with index <var class="var">idx</var>.
</p>
</dd>
<dt><samp class="option">root(expr, max)</samp></dt>
<dd><p>Find an input value for which the function represented by <var class="var">expr</var>
with argument <var class="var">ld(0)</var> is 0 in the interval 0..<var class="var">max</var>.
</p>
<p>The expression in <var class="var">expr</var> must denote a continuous function or the
result is undefined.
</p>
<p><var class="var">ld(0)</var> is used to represent the function input value, which means that the
given expression will be evaluated multiple times with various input values that
the expression can access through <code class="code">ld(0)</code>. When the expression evaluates to
0 then the corresponding input value will be returned.
</p>
</dd>
<dt><samp class="option">round(expr)</samp></dt>
<dd><p>Round the value of expression <var class="var">expr</var> to the nearest integer. For example,
&quot;round(1.5)&quot; is &quot;2.0&quot;.
</p>
</dd>
<dt><samp class="option">sgn(x)</samp></dt>
<dd><p>Compute sign of <var class="var">x</var>.
</p>
</dd>
<dt><samp class="option">sin(x)</samp></dt>
<dd><p>Compute sine of <var class="var">x</var>.
</p>
</dd>
<dt><samp class="option">sinh(x)</samp></dt>
<dd><p>Compute hyperbolic sine of <var class="var">x</var>.
</p>
</dd>
<dt><samp class="option">sqrt(expr)</samp></dt>
<dd><p>Compute the square root of <var class="var">expr</var>. This is equivalent to
&quot;(<var class="var">expr</var>)^.5&quot;.
</p>
</dd>
<dt><samp class="option">squish(x)</samp></dt>
<dd><p>Compute expression <code class="code">1/(1 + exp(4*x))</code>.
</p>
</dd>
<dt><samp class="option">st(idx, expr)</samp></dt>
<dd><p>Store the value of the expression <var class="var">expr</var> in an internal
variable. <var class="var">idx</var> specifies the index of the variable where to store
the value, and it is a value ranging from 0 to 9. The function returns
the value stored in the internal variable.
</p>
<p>The stored value can be retrieved with <code class="code">ld(var)</code>.
</p>
<p>Note: variables are currently not shared between expressions.
</p>
</dd>
<dt><samp class="option">tan(x)</samp></dt>
<dd><p>Compute tangent of <var class="var">x</var>.
</p>
</dd>
<dt><samp class="option">tanh(x)</samp></dt>
<dd><p>Compute hyperbolic tangent of <var class="var">x</var>.
</p>
</dd>
<dt><samp class="option">taylor(expr, x)</samp></dt>
<dt><samp class="option">taylor(expr, x, idx)</samp></dt>
<dd><p>Evaluate a Taylor series at <var class="var">x</var>, given an expression representing
the <code class="code">ld(idx)</code>-th derivative of a function at 0.
</p>
<p>When the series does not converge the result is undefined.
</p>
<p><var class="var">ld(idx)</var> is used to represent the derivative order in <var class="var">expr</var>,
which means that the given expression will be evaluated multiple times
with various input values that the expression can access through
<code class="code">ld(idx)</code>. If <var class="var">idx</var> is not specified then 0 is assumed.
</p>
<p>Note, when you have the derivatives at y instead of 0,
<code class="code">taylor(expr, x-y)</code> can be used.
</p>
</dd>
<dt><samp class="option">time(0)</samp></dt>
<dd><p>Return the current (wallclock) time in seconds.
</p>
</dd>
<dt><samp class="option">trunc(expr)</samp></dt>
<dd><p>Round the value of expression <var class="var">expr</var> towards zero to the nearest
integer. For example, &quot;trunc(-1.5)&quot; is &quot;-1.0&quot;.
</p>
</dd>
<dt><samp class="option">while(cond, expr)</samp></dt>
<dd><p>Evaluate expression <var class="var">expr</var> while the expression <var class="var">cond</var> is
non-zero, and returns the value of the last <var class="var">expr</var> evaluation, or
NAN if <var class="var">cond</var> was always false.
</p></dd>
</dl>

<p>The following constants are available:
</p><dl class="table">
<dt><samp class="option">PI</samp></dt>
<dd><p>area of the unit disc, approximately 3.14
</p></dd>
<dt><samp class="option">E</samp></dt>
<dd><p>exp(1) (Euler&rsquo;s number), approximately 2.718
</p></dd>
<dt><samp class="option">PHI</samp></dt>
<dd><p>golden ratio (1+sqrt(5))/2, approximately 1.618
</p></dd>
</dl>

<p>Assuming that an expression is considered &quot;true&quot; if it has a non-zero
value, note that:
</p>
<p><code class="code">*</code> works like AND
</p>
<p><code class="code">+</code> works like OR
</p>
<p>For example the construct:
</p><div class="example">
<pre class="example-preformatted">if (A AND B) then C
</pre></div>
<p>is equivalent to:
</p><div class="example">
<pre class="example-preformatted">if(A*B, C)
</pre></div>

<p>In your C code, you can extend the list of unary and binary functions,
and define recognized constants, so that they are available for your
expressions.
</p>
<p>The evaluator also recognizes the International System unit prefixes.
If &rsquo;i&rsquo; is appended after the prefix, binary prefixes are used, which
are based on powers of 1024 instead of powers of 1000.
The &rsquo;B&rsquo; postfix multiplies the value by 8, and can be appended after a
unit prefix or used alone. This allows using for example &rsquo;KB&rsquo;, &rsquo;MiB&rsquo;,
&rsquo;G&rsquo; and &rsquo;B&rsquo; as number postfix.
</p>
<p>The list of available International System prefixes follows, with
indication of the corresponding powers of 10 and of 2.
</p><dl class="table">
<dt><samp class="option">y</samp></dt>
<dd><p>10^-24 / 2^-80
</p></dd>
<dt><samp class="option">z</samp></dt>
<dd><p>10^-21 / 2^-70
</p></dd>
<dt><samp class="option">a</samp></dt>
<dd><p>10^-18 / 2^-60
</p></dd>
<dt><samp class="option">f</samp></dt>
<dd><p>10^-15 / 2^-50
</p></dd>
<dt><samp class="option">p</samp></dt>
<dd><p>10^-12 / 2^-40
</p></dd>
<dt><samp class="option">n</samp></dt>
<dd><p>10^-9 / 2^-30
</p></dd>
<dt><samp class="option">u</samp></dt>
<dd><p>10^-6 / 2^-20
</p></dd>
<dt><samp class="option">m</samp></dt>
<dd><p>10^-3 / 2^-10
</p></dd>
<dt><samp class="option">c</samp></dt>
<dd><p>10^-2
</p></dd>
<dt><samp class="option">d</samp></dt>
<dd><p>10^-1
</p></dd>
<dt><samp class="option">h</samp></dt>
<dd><p>10^2
</p></dd>
<dt><samp class="option">k</samp></dt>
<dd><p>10^3 / 2^10
</p></dd>
<dt><samp class="option">K</samp></dt>
<dd><p>10^3 / 2^10
</p></dd>
<dt><samp class="option">M</samp></dt>
<dd><p>10^6 / 2^20
</p></dd>
<dt><samp class="option">G</samp></dt>
<dd><p>10^9 / 2^30
</p></dd>
<dt><samp class="option">T</samp></dt>
<dd><p>10^12 / 2^40
</p></dd>
<dt><samp class="option">P</samp></dt>
<dd><p>10^15 / 2^50
</p></dd>
<dt><samp class="option">E</samp></dt>
<dd><p>10^18 / 2^60
</p></dd>
<dt><samp class="option">Z</samp></dt>
<dd><p>10^21 / 2^70
</p></dd>
<dt><samp class="option">Y</samp></dt>
<dd><p>10^24 / 2^80
</p></dd>
</dl>


<a name="See-Also"></a>
<h2 class="chapter">4 See Also<span class="pull-right"><a class="anchor hidden-xs" href="#See-Also" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-See-Also" aria-hidden="true">TOC</a></span></h2>

<p><a class="url" href="ffmpeg.html">ffmpeg</a>, <a class="url" href="ffplay.html">ffplay</a>, <a class="url" href="ffprobe.html">ffprobe</a>,
<a class="url" href="libavutil.html">libavutil</a>
</p>

<a name="Authors"></a>
<h2 class="chapter">5 Authors<span class="pull-right"><a class="anchor hidden-xs" href="#Authors" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Authors" aria-hidden="true">TOC</a></span></h2>

<p>The FFmpeg developers.
</p>
<p>For details about the authorship, see the Git history of the project
(https://git.ffmpeg.org/ffmpeg), e.g. by typing the command
<code class="command">git log</code> in the FFmpeg source directory, or browsing the
online repository at <a class="url" href="https://git.ffmpeg.org/ffmpeg">https://git.ffmpeg.org/ffmpeg</a>.
</p>
<p>Maintainers for the specific components are listed in the file
<samp class="file">MAINTAINERS</samp> in the source code tree.
</p>

      <p style="font-size: small;">
        This document was generated using <a class="uref" href="https://www.gnu.org/software/texinfo/"><em class="emph">makeinfo</em></a>.
      </p>
    </div>
  </body>
</html>
